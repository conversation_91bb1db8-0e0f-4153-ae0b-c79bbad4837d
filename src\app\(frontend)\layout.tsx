import type { <PERSON>ada<PERSON> } from 'next'

import { cn } from '@/utilities/ui'
import { GeistMono } from 'geist/font/mono'
import { GeistSans } from 'geist/font/sans'
import React from 'react'

import { AdminBar } from '@/components/AdminBar'
import { Footer } from '@/Footer/Component'
import { Header } from '@/Header/Component'
import { Providers } from '@/providers'
import { InitTheme } from '@/providers/Theme/InitTheme'
import { mergeOpenGraph } from '@/utilities/mergeOpenGraph'
import { draftMode } from 'next/headers'
import { getPayload } from 'payload'
import config from '@payload-config'

import './globals.css'
import { getServerSideURL } from '@/utilities/getURL'

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  // 从数据库获取站点设置
  const payload = await getPayload({ config })
  let siteSettings

  try {
    siteSettings = await payload.findGlobal({
      slug: 'siteSettings',
    })
  } catch (_error) {
    // 如果获取失败，使用默认值
    siteSettings = { headlessMode: false, maintenanceMode: false }
  }

  // 检查是否启用了 Headless 模式
  if (siteSettings.headlessMode) {
    return (
      <html lang="en">
        <head>
          <title>Frontend Disabled - Headless CMS Mode</title>
          <meta name="robots" content="noindex, nofollow" />
        </head>
        <body>
          <div>
            <h1>Frontend Disabled</h1>
            <p>This CMS is running in headless mode. Only the admin panel and API are available.</p>
            <p>Available endpoints:</p>
            <ul>
              <li>Admin Panel: /admin</li>
              <li>API: /api</li>
              <li>GraphQL: /api/graphql</li>
            </ul>
          </div>
        </body>
      </html>
    )
  }

  // 检查是否启用了维护模式
  if (siteSettings.maintenanceMode) {
    return (
      <html lang="en">
        <head>
          <title>Site Under Maintenance</title>
          <meta name="robots" content="noindex, nofollow" />
        </head>
        <body>
          <div>
            <h1>Site Under Maintenance</h1>
            <p>{siteSettings.maintenanceMessage || 'Site is currently under maintenance. Please check back later.'}</p>
          </div>
        </body>
      </html>
    )
  }

  const { isEnabled } = await draftMode()

  return (
    <html className={cn(GeistSans.variable, GeistMono.variable)} lang="en" suppressHydrationWarning>
      <head>
        <InitTheme />
        <link href="/favicon.ico" rel="icon" sizes="32x32" />
        <link href="/favicon.svg" rel="icon" type="image/svg+xml" />
      </head>
      <body>
        <Providers>
          <AdminBar
            adminBarProps={{
              preview: isEnabled,
            }}
          />

          <Header />
          {children}
          <Footer />
        </Providers>
      </body>
    </html>
  )
}

export const metadata: Metadata = {
  metadataBase: new URL(getServerSideURL()),
  openGraph: mergeOpenGraph(),
  twitter: {
    card: 'summary_large_image',
    creator: '@payloadcms',
  },
}

# 🚀 Headless CMS 模式

## 概述

此项目支持 Headless CMS 模式，可以完全禁用前端网站页面，只保留管理后台和API功能。

## 🔧 启用方式

### 环境变量配置
在 `.env` 文件中设置：
```env
HEADLESS_CMS=true
```

### 效果
- ✅ **保留功能**：`/admin/*` (管理后台) 和 `/api/*` (所有API)
- ❌ **禁用功能**：所有前端页面 (`/`, `/posts`, `/search` 等)

## 📊 访问结果

### 启用 Headless 模式时：

**前端页面访问：**
```
GET / 
GET /posts
GET /search
```
**返回：**
```json
{
  "error": "Frontend disabled",
  "message": "This CMS is running in headless mode. Only admin panel and API are available.",
  "admin": "/admin",
  "api": "/api"
}
```

**管理后台和API正常：**
```
✅ /admin          - 管理后台
✅ /api/users      - 用户API
✅ /api/posts      - 文章API
✅ /api/graphql    - GraphQL API
```

## 🔄 切换模式

### 启用前端（默认模式）
```env
# 删除或注释掉这行
# HEADLESS_CMS=true

# 或者设置为 false
HEADLESS_CMS=false
```

### 启用 Headless 模式
```env
HEADLESS_CMS=true
```

## 🎯 使用场景

### Headless 模式适用于：
- 纯API服务
- 移动应用后端
- 多前端应用（React、Vue、小程序等）
- 微服务架构中的内容管理服务

### 传统模式适用于：
- 完整的网站项目
- 需要内置前端的场景
- 快速原型开发

## 🔧 技术实现

使用 Next.js 中间件在请求层面拦截，确保：
- 零性能损耗（不执行页面组件）
- 完全阻止前端访问
- 保持API和管理后台完整功能

## 📝 注意事项

1. **构建时**：无论是否启用 Headless 模式，都会构建所有页面
2. **运行时**：通过中间件动态控制访问权限
3. **部署**：可以通过环境变量在不同环境使用不同模式

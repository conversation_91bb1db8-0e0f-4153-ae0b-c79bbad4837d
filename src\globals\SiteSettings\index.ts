import type { GlobalConfig } from 'payload'

import { authenticated } from '../../access/authenticated'

export const SiteSettings: GlobalConfig = {
  slug: 'siteSettings',
  label: {
    en: 'Site Settings',
    zh: '站点设置',
  },
  access: {
    read: authenticated,
    update: authenticated,
  },
  admin: {
    group: {
      en: 'System',
      zh: '系统',
    },
  },
  fields: [
    {
      name: 'headlessMode',
      type: 'checkbox',
      label: {
        en: 'Enable Headless Mode',
        zh: '启用无头模式',
      },
      admin: {
        description: {
          en: 'When enabled, the frontend website will be disabled and only the admin panel and API will be accessible. This is useful for using the CMS as a headless content management system.',
          zh: '启用后，前端网站将被禁用，只有管理面板和API可以访问。这对于将CMS用作无头内容管理系统很有用。',
        },
      },
      defaultValue: false,
    },
    {
      name: 'maintenanceMode',
      type: 'checkbox',
      label: {
        en: 'Enable Maintenance Mode',
        zh: '启用维护模式',
      },
      admin: {
        description: {
          en: 'When enabled, the frontend will show a maintenance message. Admin panel remains accessible.',
          zh: '启用后，前端将显示维护信息。管理面板仍可访问。',
        },
      },
      defaultValue: false,
    },
    {
      name: 'maintenanceMessage',
      type: 'textarea',
      label: {
        en: 'Maintenance Message',
        zh: '维护信息',
      },
      admin: {
        condition: (data) => data.maintenanceMode,
        description: {
          en: 'Custom message to display during maintenance mode.',
          zh: '维护模式期间显示的自定义信息。',
        },
      },
      defaultValue: 'Site is currently under maintenance. Please check back later.',
    },
  ],
}

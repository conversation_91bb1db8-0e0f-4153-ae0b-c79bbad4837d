import Link from 'next/link'
import React from 'react'

import { Button } from '@/components/ui/button'
import { getSiteSettings } from '@/utilities/getSiteSettings'

export default async function NotFound() {
  const siteSettings = await getSiteSettings()

  // 如果是headless模式，显示特殊信息
  if (siteSettings.headlessMode) {
    return (
      <div className="container py-28">
        <div className="prose max-w-none">
          <h1 style={{ marginBottom: 0 }}>Frontend Disabled</h1>
          <p className="mb-4">This CMS is running in headless mode. Only the admin panel and API are available.</p>
          <div className="mb-4">
            <p>Available endpoints:</p>
            <ul>
              <li>Admin Panel: <Link href="/admin" className="text-blue-600 hover:underline">/admin</Link></li>
              <li>API: <Link href="/api" className="text-blue-600 hover:underline">/api</Link></li>
              <li>GraphQL: <Link href="/api/graphql" className="text-blue-600 hover:underline">/api/graphql</Link></li>
            </ul>
          </div>
        </div>
        <Button asChild variant="default">
          <Link href="/admin">Go to Admin Panel</Link>
        </Button>
      </div>
    )
  }

  // 如果是维护模式，显示维护信息
  if (siteSettings.maintenanceMode) {
    return (
      <div className="container py-28">
        <div className="prose max-w-none">
          <h1 style={{ marginBottom: 0 }}>Site Under Maintenance</h1>
          <p className="mb-4">{siteSettings.maintenanceMessage || 'Site is currently under maintenance. Please check back later.'}</p>
        </div>
        <Button asChild variant="default">
          <Link href="/admin">Go to Admin Panel</Link>
        </Button>
      </div>
    )
  }

  // 正常的404页面
  return (
    <div className="container py-28">
      <div className="prose max-w-none">
        <h1 style={{ marginBottom: 0 }}>404</h1>
        <p className="mb-4">This page could not be found.</p>
      </div>
      <Button asChild variant="default">
        <Link href="/">Go home</Link>
      </Button>
    </div>
  )
}

import Link from 'next/link'
import React from 'react'

import { Button } from '@/components/ui/button'
import { getSiteSettings } from '@/utilities/getSiteSettings'

export default async function NotFound() {
  const siteSettings = await getSiteSettings()

  // 如果是headless模式，显示简洁的404页面
  if (siteSettings.headlessMode) {
    return (
      <div className="container py-28">
        <div className="prose max-w-none">
          <h1 style={{ marginBottom: 0 }}>404</h1>
          <p className="mb-4">This page could not be found.</p>
        </div>
      </div>
    )
  }

  // 如果是维护模式，显示维护信息
  if (siteSettings.maintenanceMode) {
    return (
      <div className="container py-28">
        <div className="prose max-w-none">
          <h1 style={{ marginBottom: 0 }}>Site Under Maintenance</h1>
          <p className="mb-4">{siteSettings.maintenanceMessage || 'Site is currently under maintenance. Please check back later.'}</p>
        </div>
        <Button asChild variant="default">
          <Link href="/admin">Go to Admin Panel</Link>
        </Button>
      </div>
    )
  }

  // 正常的404页面
  return (
    <div className="container py-28">
      <div className="prose max-w-none">
        <h1 style={{ marginBottom: 0 }}>404</h1>
        <p className="mb-4">This page could not be found.</p>
      </div>
      <Button asChild variant="default">
        <Link href="/">Go home</Link>
      </Button>
    </div>
  )
}

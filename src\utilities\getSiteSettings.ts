import { getPayload } from 'payload'
import config from '@payload-config'

export interface SiteSettings {
  headlessMode: boolean
  maintenanceMode: boolean
  maintenanceMessage?: string
}

export async function getSiteSettings(): Promise<SiteSettings> {
  try {
    const payload = await getPayload({ config })
    const siteSettings = await payload.findGlobal({
      slug: 'siteSettings',
    })
    
    return {
      headlessMode: siteSettings.headlessMode || false,
      maintenanceMode: siteSettings.maintenanceMode || false,
      maintenanceMessage: siteSettings.maintenanceMessage || 'Site is currently under maintenance. Please check back later.',
    }
  } catch (error) {
    // 如果获取失败，使用默认值
    return {
      headlessMode: false,
      maintenanceMode: false,
      maintenanceMessage: 'Site is currently under maintenance. Please check back later.',
    }
  }
}

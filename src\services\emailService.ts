import type { Payload } from 'payload'
import * as nodemailer from 'nodemailer'

export interface EmailConfig {
  name: string
  isActive?: boolean | null
  host: string
  port: number
  secure?: boolean | null
  username: string
  password: string
  fromEmail: string
  fromName?: string | null
  testEmail?: string | null
  testSubject?: string | null
  testContent?: string | null
}

export interface EmailData {
  to: string | string[]
  subject: string
  html?: string
  text?: string
  cc?: string | string[]
  bcc?: string | string[]
  replyTo?: string
}

/**
 * 获取当前活跃的邮件配置
 */
export async function getActiveEmailConfig(payload: Payload): Promise<EmailConfig | null> {
  try {
    const emailService = await payload.findGlobal({
      slug: 'emailService',
    })

    if (!emailService.emailConfigs || emailService.emailConfigs.length === 0) {
      payload.logger.warn('No email configurations found')
      return null
    }

    const activeConfig = emailService.emailConfigs.find(
      (config: EmailConfig) => config.isActive === true,
    )

    if (!activeConfig) {
      payload.logger.warn('No active email configuration found')
      return null
    }

    return activeConfig
  } catch (error) {
    payload.logger.error('Error fetching active email configuration:', error)
    return null
  }
}

/**
 * 创建 nodemailer 传输器
 */
export async function createEmailTransporter(payload: Payload) {
  const activeConfig = await getActiveEmailConfig(payload)

  if (!activeConfig) {
    throw new Error('No active email configuration available')
  }

  const transporter = nodemailer.createTransport({
    host: activeConfig.host,
    port: activeConfig.port,
    secure: Boolean(activeConfig.secure),
    auth: {
      user: activeConfig.username,
      pass: activeConfig.password,
    },
    tls: {
      rejectUnauthorized: false, // 允许自签名证书
    },
  })

  return { transporter, config: activeConfig }
}

/**
 * 使用活跃配置发送邮件
 */
export async function sendEmailWithActiveConfig(
  payload: Payload,
  emailData: EmailData,
): Promise<any> {
  try {
    const { transporter, config } = await createEmailTransporter(payload)

    const mailOptions = {
      from: `${config.fromName || 'Website'} <${config.fromEmail}>`,
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text,
      cc: emailData.cc,
      bcc: emailData.bcc,
      replyTo: emailData.replyTo,
    }

    const result = await transporter.sendMail(mailOptions)

    payload.logger.info(`Email sent successfully using config: ${config.name}`)
    return result
  } catch (error) {
    payload.logger.error('Error sending email:', error)
    throw error
  }
}

/**
 * 测试邮件配置
 */
export async function testEmailConfiguration(
  payload: Payload,
  configName?: string,
): Promise<{ success: boolean; message: string; error?: any }> {
  try {
    const emailService = await payload.findGlobal({
      slug: 'emailService',
    })

    if (!emailService.emailConfigs || emailService.emailConfigs.length === 0) {
      return {
        success: false,
        message: '没有找到邮件配置',
      }
    }

    // 如果指定了配置名称，使用指定的配置，否则使用活跃配置
    let targetConfig: EmailConfig | undefined
    if (configName) {
      targetConfig = emailService.emailConfigs.find(
        (config: EmailConfig) => config.name === configName,
      )
    } else {
      targetConfig = emailService.emailConfigs.find(
        (config: EmailConfig) => config.isActive === true,
      )
    }

    if (!targetConfig) {
      return {
        success: false,
        message: configName ? `未找到名为 "${configName}" 的配置` : '没有活跃的邮件配置',
      }
    }

    if (!targetConfig.testEmail) {
      return {
        success: false,
        message: '该配置没有设置测试邮箱地址',
      }
    }

    // 创建测试传输器
    const transporter = nodemailer.createTransport({
      host: targetConfig.host,
      port: targetConfig.port,
      secure: Boolean(targetConfig.secure),
      auth: {
        user: targetConfig.username,
        pass: targetConfig.password,
      },
      tls: {
        rejectUnauthorized: false, // 允许自签名证书
      },
    })

    // 发送测试邮件
    const testResult = await transporter.sendMail({
      from: `${targetConfig.fromName || 'Test Email'} <${targetConfig.fromEmail}>`,
      to: targetConfig.testEmail,
      subject: targetConfig.testSubject || '邮件配置测试 - Email Configuration Test',
      html: `
        <h2>邮件配置测试成功！</h2>
        <p>这是一封测试邮件，用于验证邮件配置是否正常工作。</p>
        <hr>
        <h2>Email Configuration Test Successful!</h2>
        <p>This is a test email to verify that the email configuration is working properly.</p>
        <br>
        <p><strong>配置名称 / Configuration:</strong> ${targetConfig.name}</p>
        <p><strong>发送时间 / Sent at:</strong> ${new Date().toLocaleString()}</p>
      `,
      text: `
        邮件配置测试成功！
        这是一封测试邮件，用于验证邮件配置是否正常工作。
        
        Email Configuration Test Successful!
        This is a test email to verify that the email configuration is working properly.
        
        配置名称 / Configuration: ${targetConfig.name}
        发送时间 / Sent at: ${new Date().toLocaleString()}
      `,
    })

    payload.logger.info(`Test email sent successfully for config: ${targetConfig.name}`)

    return {
      success: true,
      message: `测试邮件已发送到 ${targetConfig.testEmail}`,
    }
  } catch (error) {
    payload.logger.error('Error testing email configuration:', error)
    return {
      success: false,
      message: '邮件配置测试失败',
      error: error instanceof Error ? error.message : String(error),
    }
  }
}

/**
 * 获取所有邮件配置
 */
export async function getAllEmailConfigs(payload: Payload): Promise<EmailConfig[]> {
  try {
    const emailService = await payload.findGlobal({
      slug: 'emailService',
    })

    return emailService.emailConfigs || []
  } catch (error) {
    payload.logger.error('Error fetching email configurations:', error)
    return []
  }
}

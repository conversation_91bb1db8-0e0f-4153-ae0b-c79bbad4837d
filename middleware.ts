import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  console.log('🔥 Middleware executed for:', request.nextUrl.pathname)

  // 直接检查环境变量
  const headlessCms = process.env.HEADLESS_CMS
  console.log('🔥 HEADLESS_CMS env var:', headlessCms)

  const isHeadless = headlessCms === 'true'
  console.log('🔥 isHeadless:', isHeadless)

  if (isHeadless) {
    const { pathname } = request.nextUrl
    console.log('🔥 Checking pathname:', pathname)

    // 允许管理后台和API路由
    if (
      pathname.startsWith('/admin') ||
      pathname.startsWith('/api') ||
      pathname.startsWith('/_next') ||
      pathname.startsWith('/favicon') ||
      pathname === '/robots.txt' ||
      pathname === '/sitemap.xml'
    ) {
      console.log('🔥 Allowing path:', pathname)
      return NextResponse.next()
    }

    console.log('🔥 Blocking path:', pathname)
    // 阻止所有前端页面访问
    return new NextResponse(
      JSON.stringify({
        error: 'Frontend disabled',
        message: 'This CMS is running in headless mode. Only admin panel and API are available.',
        admin: '/admin',
        api: '/api',
        debug: {
          pathname,
          headlessCms,
          isHeadless
        }
      }),
      {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }

  console.log('🔥 Not headless, allowing all')
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}

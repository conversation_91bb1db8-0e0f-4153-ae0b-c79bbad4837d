{"name": "payload-vercel-website-template", "version": "1.0.0", "description": "Website template for Payload", "license": "MIT", "type": "module", "scripts": {"check-env": "node scripts/check-env.cjs", "init-db": "node scripts/init-db.cjs", "prebuild": "pnpm check-env && pnpm init-db", "build": "cross-env NODE_OPTIONS=--no-deprecation next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "build:safe": "pnpm prebuild && pnpm build", "ci": "pnpm build:safe", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "dev:prod": "cross-env NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build:safe && pnpm start", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "ii": "cross-env NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "migrate": "cross-env NODE_OPTIONS=--no-deprecation payload migrate", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "reinstall": "cross-env NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@payloadcms/admin-bar": "3.43.0", "@payloadcms/db-postgres": "^3.43.0", "@payloadcms/db-vercel-postgres": "3.43.0", "@payloadcms/live-preview-react": "3.43.0", "@payloadcms/next": "3.43.0", "@payloadcms/payload-cloud": "3.43.0", "@payloadcms/plugin-form-builder": "3.43.0", "@payloadcms/plugin-nested-docs": "3.43.0", "@payloadcms/plugin-redirects": "3.43.0", "@payloadcms/plugin-search": "3.43.0", "@payloadcms/plugin-seo": "3.43.0", "@payloadcms/richtext-lexical": "3.43.0", "@payloadcms/storage-vercel-blob": "3.43.0", "@payloadcms/translations": "^3.43.0", "@payloadcms/ui": "3.43.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "geist": "^1.3.0", "graphql": "^16.8.2", "lucide-react": "^0.378.0", "next": "15.3.0", "next-sitemap": "^4.2.3", "nodemailer": "^7.0.3", "payload": "3.43.0", "pg": "^8.16.2", "prism-react-renderer": "^2.3.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.45.4", "sharp": "0.32.6", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@tailwindcss/typography": "^0.5.13", "@types/escape-html": "^1.0.2", "@types/node": "22.5.4", "@types/nodemailer": "^6.4.17", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "autoprefixer": "^10.4.19", "copyfiles": "^2.4.1", "dotenv": "^16.5.0", "eslint": "^9.16.0", "eslint-config-next": "15.3.0", "postcss": "^8.4.38", "prettier": "^3.4.2", "tailwindcss": "^3.4.3", "typescript": "5.7.3"}, "packageManager": "pnpm@10.12.1", "engines": {"node": "^18.20.2 || >=20.9.0"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}
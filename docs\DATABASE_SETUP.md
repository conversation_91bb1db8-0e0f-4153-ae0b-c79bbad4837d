# 数据库设置指南

## 🚨 重要：避免构建时数据库错误

为了避免在新项目中遇到 `relation "posts" does not exist` 错误，请按照以下步骤操作：

## 📋 新项目设置清单

### 1. 环境变量配置
确保 `.env` 文件包含所有必需的变量：

```env
# 数据库连接字符串
POSTGRES_URL=postgres://username:password@host:port/database

# JWT 加密密钥
PAYLOAD_SECRET=your_secret_here

# Cron 作业密钥
CRON_SECRET=your_cron_secret_here

# 预览密钥
PREVIEW_SECRET=your_preview_secret_here

# 邮件服务（可选）
RESEND_API_KEY=your_resend_key_here
```

### 2. 正确的构建顺序

**❌ 错误做法：**
```bash
pnpm build  # 直接构建会失败
```

**✅ 正确做法：**
```bash
# 方法1：使用安全构建脚本
pnpm build:safe

# 方法2：手动步骤
pnpm check-env    # 检查环境变量
pnpm init-db      # 初始化数据库
pnpm build        # 构建项目

# 方法3：CI/CD 环境
pnpm ci
```

### 3. 开发环境启动

```bash
# 首次启动开发环境
pnpm check-env    # 检查环境
pnpm init-db      # 初始化数据库
pnpm dev          # 启动开发服务器
```

## 🔧 可用脚本说明

| 脚本 | 用途 | 何时使用 |
|------|------|----------|
| `pnpm check-env` | 检查环境变量 | 项目设置时 |
| `pnpm init-db` | 初始化数据库 | 首次运行或数据库重置后 |
| `pnpm build:safe` | 安全构建（包含预检查） | 生产构建 |
| `pnpm ci` | CI/CD 构建 | 自动化部署 |
| `pnpm migrate` | 手动运行迁移 | 数据库结构更新后 |

## 🚀 部署到生产环境

### Vercel 部署
在 Vercel 项目设置中：

1. **Build Command:** `pnpm ci`
2. **Install Command:** `pnpm install`
3. **环境变量：** 添加所有必需的环境变量

### Docker 部署
```dockerfile
# 在 Dockerfile 中确保正确的构建顺序
RUN pnpm install
RUN pnpm build:safe
```

## 🔍 故障排除

### 问题：`relation "posts" does not exist`
**原因：** 数据库迁移未运行
**解决：** 
```bash
pnpm init-db
pnpm build
```

### 问题：环境变量未找到
**原因：** `.env` 文件缺失或变量未设置
**解决：**
```bash
pnpm check-env  # 查看具体缺失的变量
```

### 问题：数据库连接失败
**原因：** `POSTGRES_URL` 格式错误或数据库不可访问
**解决：**
1. 检查数据库 URL 格式
2. 确认数据库服务运行中
3. 检查网络连接

## 📝 最佳实践

1. **总是先检查环境：** `pnpm check-env`
2. **使用安全构建：** `pnpm build:safe` 而不是 `pnpm build`
3. **CI/CD 使用：** `pnpm ci` 命令
4. **定期备份数据库**
5. **版本控制忽略 `.env` 文件**

## 🎯 新团队成员快速开始

```bash
# 1. 克隆项目
git clone <repository-url>
cd payload_cms

# 2. 安装依赖
pnpm install

# 3. 复制环境变量模板
cp .env.example .env  # 如果有的话

# 4. 编辑 .env 文件，设置数据库连接等

# 5. 检查环境配置
pnpm check-env

# 6. 初始化数据库
pnpm init-db

# 7. 启动开发服务器
pnpm dev
```

遵循这些步骤，您将永远不会再遇到数据库相关的构建错误！

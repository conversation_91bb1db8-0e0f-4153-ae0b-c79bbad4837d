import type { GlobalConfig } from 'payload'
import { ensureOneActiveEmailConfig } from './hooks/ensureOneActive'

export const EmailService: GlobalConfig = {
  slug: 'emailService',
  label: {
    en: 'Email Service',
    zh: '邮件服务配置',
  },
  access: {
    read: () => true,
    update: () => true, // 暂时允许所有登录用户修改，后续可以根据需要调整
  },
  admin: {
    description: {
      en: 'Configure email service settings for the application. You can add multiple email configurations and set one as active.',
      zh: '配置应用程序的邮件服务设置。您可以添加多个邮箱配置并设置其中一个为生效状态。',
    },
  },
  fields: [
    {
      name: 'emailConfigs',
      type: 'array',
      label: {
        en: 'Email Configurations',
        zh: '邮箱配置',
      },
      labels: {
        singular: {
          en: 'Email Config',
          zh: '邮箱配置',
        },
        plural: {
          en: 'Email Configs',
          zh: '邮箱配置',
        },
      },
      admin: {
        description: {
          en: 'Add multiple email configurations. Only one can be active at a time.',
          zh: '添加多个邮箱配置。同时只能有一个配置处于生效状态。',
        },
        initCollapsed: true,
      },
      fields: [
        {
          name: 'name',
          type: 'text',
          label: {
            en: 'Configuration Name',
            zh: '配置名称',
          },
          admin: {
            description: {
              en: 'A friendly name to identify this email configuration',
              zh: '用于识别此邮箱配置的友好名称',
            },
          },
          required: true,
        },
        {
          name: 'isActive',
          type: 'checkbox',
          label: {
            en: 'Active Configuration',
            zh: '当前生效配置',
          },
          admin: {
            description: {
              en: 'Only one configuration can be active at a time. This will be used for sending emails.',
              zh: '同时只能有一个配置处于生效状态。此配置将用于发送邮件。',
            },
          },
          defaultValue: false,
        },
        {
          type: 'row',
          fields: [
            {
              name: 'host',
              type: 'text',
              label: {
                en: 'SMTP Host',
                zh: 'SMTP服务器',
              },
              admin: {
                description: {
                  en: 'SMTP server hostname (e.g., smtp.gmail.com)',
                  zh: 'SMTP服务器主机名（例如：smtp.gmail.com）',
                },
                width: '50%',
              },
              required: true,
            },
            {
              name: 'port',
              type: 'number',
              label: {
                en: 'Port',
                zh: '端口',
              },
              admin: {
                description: {
                  en: 'SMTP port (usually 587 for TLS, 465 for SSL, 25 for non-secure)',
                  zh: 'SMTP端口（通常587用于TLS，465用于SSL，25用于非安全连接）',
                },
                width: '50%',
              },
              defaultValue: 587,
              required: true,
            },
          ],
        },
        {
          name: 'secure',
          type: 'checkbox',
          label: {
            en: 'Use SSL/TLS',
            zh: '使用SSL/TLS',
          },
          admin: {
            description: {
              en: 'Enable secure connection (recommended for production)',
              zh: '启用安全连接（生产环境推荐）',
            },
          },
          defaultValue: true,
        },
        {
          type: 'row',
          fields: [
            {
              name: 'username',
              type: 'text',
              label: {
                en: 'Username',
                zh: '用户名',
              },
              admin: {
                description: {
                  en: 'SMTP authentication username (usually your email address)',
                  zh: 'SMTP认证用户名（通常是您的邮箱地址）',
                },
                width: '50%',
              },
              required: true,
            },
            {
              name: 'password',
              type: 'text',
              label: {
                en: 'Password',
                zh: '密码',
              },
              admin: {
                description: {
                  en: 'SMTP authentication password or app-specific password',
                  zh: 'SMTP认证密码或应用专用密码',
                },
                width: '50%',
              },
              required: true,
            },
          ],
        },
        {
          type: 'row',
          fields: [
            {
              name: 'fromEmail',
              type: 'email',
              label: {
                en: 'From Email',
                zh: '发件邮箱',
              },
              admin: {
                description: {
                  en: 'The email address that will appear as the sender',
                  zh: '将显示为发件人的邮箱地址',
                },
                width: '50%',
              },
              required: true,
            },
            {
              name: 'fromName',
              type: 'text',
              label: {
                en: 'From Name',
                zh: '发件人名称',
              },
              admin: {
                description: {
                  en: 'The name that will appear as the sender',
                  zh: '将显示为发件人的名称',
                },
                width: '50%',
              },
              defaultValue: 'Website Contact Form',
            },
          ],
        },
        {
          name: 'testEmail',
          type: 'email',
          label: {
            en: 'Test Email Address',
            zh: '测试邮箱地址',
          },
          admin: {
            description: {
              en: 'Email address to send test emails to verify this configuration',
              zh: '用于发送测试邮件以验证此配置的邮箱地址',
            },
          },
        },
        {
          name: 'testSubject',
          type: 'text',
          label: {
            en: 'Test Email Subject',
            zh: '测试邮件主题',
          },
          admin: {
            description: {
              en: 'Subject line for test emails',
              zh: '测试邮件的主题行',
            },
          },
          defaultValue: '邮件配置测试 - Email Configuration Test',
        },
        {
          name: 'testContent',
          type: 'textarea',
          label: {
            en: 'Test Email Content',
            zh: '测试邮件内容',
          },
          admin: {
            description: {
              en: 'Content for test emails',
              zh: '测试邮件的内容',
            },
            rows: 3,
          },
          defaultValue:
            '这是一封测试邮件，用于验证邮件配置是否正常工作。如果您收到这封邮件，说明配置成功！',
        },
        {
          name: 'testButton',
          type: 'ui',
          admin: {
            components: {
              Field: '@/globals/EmailService/components/EmailTestButton#EmailTestButton',
            },
          },
        },
      ],
      minRows: 1,
      maxRows: 10,
    },
  ],
  hooks: {
    afterChange: [ensureOneActiveEmailConfig],
  },
}
